{"name": "znsb-tzzx", "description": "znsb-tzzx", "version": "0.1.0-alpha.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "dev:mock": "cross-env VUE_APP_MOCK=true nodemon -x vue-cli-service serve", "serve": "vue-cli-service serve", "build:site": "vue-cli-service build", "build": "vue-cli-service build", "build:site:dev": "vue-cli-service build --mode development", "build:site:test": "vue-cli-service build --mode test", "lint": "vue-cli-service lint", "lint:style": "vue-cli-service lint:style", "changelog": "conventional-changelog -p custom-config -i CHANGELOG.md -s -r 0", "commit": "git-cz", "@vue/babel-helper-vue-jsx-merge-props": "1.4.0", "@vue/babel-preset-jsx": "1.4.0"}, "dependencies": {"@gt4/common-front": "2.0.113", "@gtff/tdesign-gt-vue": "1.4.0", "@wecity/tdesign-vue-ie": "1.3.1", "axios": "^0.21.1", "babel-polyfill": "^6.26.0", "clipboard": "^2.0.8", "core-js": "^3.6.5", "css-split-webpack-plugin": "0.2.6", "dayjs": "^1.10.4", "lodash-es": "^4.17.21", "qrcodejs2": "0.0.3", "regenerator-runtime": "^0.13.9", "tdesign-icons-vue": "0.0.8", "tdesign-vue": "1.4.0", "vue": "~2.6.11", "vue-router": "3.2.0", "vue-pdf": "^4.3.0", "vuex": "^3.4.0", "nanoid": "3.3.7"}, "devDependencies": {"@commitlint/cli": "^12.1.1", "@commitlint/config-conventional": "^12.1.1", "@samhammer/vue-cli-plugin-stylelint": "^2.0.1", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@wecity/static-env-config": "1.0.32", "babel-eslint": "^10.1.0", "commitizen": "^4.2.3", "conventional-changelog-cli": "^2.1.1", "conventional-changelog-custom-config": "^0.3.1", "cross-env": "^7.0.3", "css-properties-sorting": "^1.0.10", "eslint": "^6.7.2", "eslint-config-airbnb-base": "^14.2.1", "eslint-config-prettier": "^8.3.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.24.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^6.2.2", "glob": "^7.1.6", "globby": "^11.0.3", "hard-source-webpack-plugin": "^0.13.1", "less": "^3.0.4", "less-loader": "^5.0.0", "lint-staged": "^10.5.4", "mockjs": "^1.1.0", "modify-source-webpack-plugin": "^3.0.0", "node-object-hash": "^2.3.8", "nodemon": "^2.0.7", "npm-run-all": "^4.1.5", "prettier": "^2.3.2", "rimraf": "^3.0.2", "style-resources-loader": "^1.4.1", "stylelint": "^13.12.0", "stylelint-config-prettier": "^8.0.2", "stylelint-config-tencent": "1.0.0", "stylelint-order": "^4.1.0", "terser-webpack-plugin": "4.2.3", "vue-cli-plugin-style-resources-loader": "~0.1.5", "vue-template-compiler": "~2.6.11"}, "config": {"commitizen": {"path": "cz-customizable"}}, "lint-staged": {"src/**/*.{js,vue}": "yarn lint", "src/**/*.{vue,js,scss,less,css,html}": "yarn lint:style"}, "changelog": {"emojis": true, "authorName": true, "authorEmail": false}, "main": "index.js", "repository": "http://10.23.12.27:8089/qyd-znsb/znsb-tzzx.git", "license": "MIT"}